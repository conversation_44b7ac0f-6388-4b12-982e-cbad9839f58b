-- =====================================================
-- RESTORE DELETED BOOKING: CY-310825-75775
-- Customer: <PERSON><PERSON> (**********)
-- Trip: Bahadurgarh to Jaipur (One Way)
-- Date: 01/09/2025 | Time: 08:00
-- =====================================================

-- Step 1: Get/Create User Account
DECLARE @UserId UNIQUEIDENTIFIER;
DECLARE @CityFromId INT;
DECLARE @CityToId INT;
DECLARE @TripTypeId INT;
DECLARE @CarCategoryId INT;

-- Check if user exists, if not create one
SELECT @UserId = Id FROM [Identity].[User] WHERE PhoneNumber = '**********';

IF @UserId IS NULL
BEGIN
    SET @UserId = NEWID();
    
    INSERT INTO [Identity].[User] (
        Id, UserName, NormalizedUserName, Email, NormalizedEmail, 
        EmailConfirmed, PasswordHash, SecurityStamp, ConcurrencyStamp,
        PhoneNumber, PhoneNumberConfirmed, TwoFactorEnabled, 
        LockoutEnabled, AccessFailedCount, FirstName, LastName,
        IsActive, CreatedDate, UpdatedDate
    )
    VALUES (
        @UserId, 
        'ashok.kumar.**********', 
        'ASHOK.KUMAR.**********',
        NULL, 
        NULL,
        0, 
        NULL, 
        NEWID(), 
        NEWID(),
        '**********', 
        1, 
        0, 
        1, 
        0, 
        'Ashok', 
        'Kumar',
        1, 
        GETDATE(), 
        GETDATE()
    );
    
    PRINT 'Created new user account for Ashok Kumar (**********)';
END
ELSE
BEGIN
    PRINT 'Found existing user account for Ashok Kumar (**********)';
END

-- Step 2: Get City IDs
SELECT @CityFromId = PKID FROM RLT_CITY WHERE City_Name = 'Bahadurgarh' AND Is_Active = 1;
SELECT @CityToId = PKID FROM RLT_CITY WHERE City_Name = 'Jaipur' AND Is_Active = 1;

-- If cities don't exist, create them
IF @CityFromId IS NULL
BEGIN
    INSERT INTO RLT_CITY (PK_GUID, City_Name, City_Abbr, Is_Active, Created_Date, Updated_Date, orderIndex)
    VALUES (NEWID(), 'Bahadurgarh', 'BGH', 1, GETDATE(), GETDATE(), 999);
    
    SELECT @CityFromId = SCOPE_IDENTITY();
    PRINT 'Created city: Bahadurgarh';
END

IF @CityToId IS NULL
BEGIN
    INSERT INTO RLT_CITY (PK_GUID, City_Name, City_Abbr, Is_Active, Created_Date, Updated_Date, orderIndex)
    VALUES (NEWID(), 'Jaipur', 'JPR', 1, GETDATE(), GETDATE(), 1);
    
    SELECT @CityToId = SCOPE_IDENTITY();
    PRINT 'Created city: Jaipur';
END

-- Step 3: Get Trip Type ID (One Way)
SELECT @TripTypeId = PKID FROM RLT_TRIP_TYPES WHERE Trip_Type = 'One Way' AND Is_Active = 1;

-- Step 4: Get Car Category ID (SUV for Ertiga)
SELECT @CarCategoryId = PKID FROM RLT_CAR_CATEGORY WHERE Car_Category_Abbr = 'SUV' AND Is_Active = 1;

-- Step 5: Insert the restored booking
INSERT INTO RLT_BOOKING (
    Booking_Id,
    City_From_Id,
    City_To_Id,
    Trip_Type_Id,
    Car_Category_Id,
    Duration,
    Distance,
    Basic_Fare,
    Driver_Charge,
    GST,
    Fare,
    GST_Fare,
    Coupon_Code,
    Coupon_Discount,
    Booking_Date,
    PickUp_Address,
    DropOff_Address,
    PickUp_Date,
    PickUp_Time,
    Name,
    Mobile_No1,
    Mobile_No2,
    Mail_Id,
    Mode_Of_Payment_Id,
    Vendor_Id,
    Car_Id,
    Booking_Status_Id,
    Booking_Remark,
    Invoice_No,
    Invoice_Date,
    Is_Active,
    Created_Date,
    Updated_Date,
    Created_By,
    Updated_By,
    razorpay_payment_id,
    razorpay_order_id,
    razorpay_signature,
    razorpay_status,
    PickUpAddressLatitude,
    PickUpAddressLongitude,
    BookingEditRemark,
    driver_id,
    completepickupaddress,
    completedropoffpaddress,
    IsFullOnlinePayment,
    CashAmountToPayDriver,
    TollCharge,
    PaymentOption,
    DropOffDate,
    DropOffTime,
    PaymentDate,
    BookedBy,
    IsAdminBooked,
    PaymentType,
    PartialPaymentAmount,
    RemainingAmountForDriver,
    Booking_Created_By,
    payment_link
)
VALUES (
    'CY-310825-75775',                          -- Booking_Id
    @CityFromId,                                -- City_From_Id (Bahadurgarh)
    @CityToId,                                  -- City_To_Id (Jaipur)
    @TripTypeId,                                -- Trip_Type_Id (One Way)
    @CarCategoryId,                             -- Car_Category_Id (SUV)
    NULL,                                       -- Duration
    NULL,                                       -- Distance
    5678.00,                                    -- Basic_Fare (Total Fare before discount)
    NULL,                                       -- Driver_Charge
    NULL,                                       -- GST
    5500.00,                                    -- Fare (Final amount after discount)
    NULL,                                       -- GST_Fare
    NULL,                                       -- Coupon_Code
    178.00,                                     -- Coupon_Discount (discount amount)
    '2025-08-31',                               -- Booking_Date (today - when booking was made)
    'Bahadurgarh',                              -- PickUp_Address
    'Jaipur',                                   -- DropOff_Address
    '2025-09-01',                               -- PickUp_Date
    '08:00',                                    -- PickUp_Time
    'Ashok Kumar',                              -- Name
    '**********',                               -- Mobile_No1
    NULL,                                       -- Mobile_No2
    NULL,                                       -- Mail_Id
    1,                                          -- Mode_Of_Payment_Id (UPI/Online)
    NULL,                                       -- Vendor_Id
    NULL,                                       -- Car_Id
    2,                                          -- Booking_Status_Id (Confirmed/Active)
    'Restored booking - PhonePe payment',      -- Booking_Remark
    NULL,                                       -- Invoice_No
    NULL,                                       -- Invoice_Date
    1,                                          -- Is_Active
    GETDATE(),                                  -- Created_Date
    GETDATE(),                                  -- Updated_Date
    1,                                          -- Created_By (Admin)
    1,                                          -- Updated_By (Admin)
    'OM2508312212407358540121',                 -- razorpay_payment_id (PhonePe Transaction ID)
    'OMO2508312212368891069977',                -- razorpay_order_id (Transaction ID)
    NULL,                                       -- razorpay_signature
    'Paid',                                     -- razorpay_status
    NULL,                                       -- PickUpAddressLatitude
    NULL,                                       -- PickUpAddressLongitude
    'Booking restored from deletion',           -- BookingEditRemark
    NULL,                                       -- driver_id
    'Bahadurgarh',                              -- completepickupaddress
    'Jaipur',                                   -- completedropoffpaddress
    1,                                          -- IsFullOnlinePayment
    4148.00,                                    -- CashAmountToPayDriver (5500 - 1352 advance)
    NULL,                                       -- TollCharge
    1,                                          -- PaymentOption (Online)
    NULL,                                       -- DropOffDate
    NULL,                                       -- DropOffTime
    '2025-08-31',                               -- PaymentDate (advance payment date)
    'Customer',                                 -- BookedBy
    0,                                          -- IsAdminBooked
    'PARTIAL',                                  -- PaymentType (partial payment made)
    1352.00,                                    -- PartialPaymentAmount (advance paid)
    4148.00,                                    -- RemainingAmountForDriver (balance amount)
    @UserId,                                    -- Booking_Created_By (User GUID)
    NULL                                        -- payment_link
);

-- Step 6: Verification Query
SELECT 
    'BOOKING RESTORED SUCCESSFULLY' as Status,
    b.Booking_Id,
    b.Name,
    b.Mobile_No1,
    cf.City_Name as FromCity,
    ct.City_Name as ToCity,
    tt.Trip_Type,
    cc.Car_Category_Abbr as CarCategory,
    b.PickUp_Date,
    b.PickUp_Time,
    b.Fare as TotalFare,
    b.Coupon_Discount as Discount,
    b.PartialPaymentAmount as AdvancePaid,
    b.RemainingAmountForDriver as RemainingAmount,
    b.razorpay_status as PaymentStatus,
    b.razorpay_payment_id as PhonePeTransactionId
FROM RLT_BOOKING b
INNER JOIN RLT_CITY cf ON b.City_From_Id = cf.PKID
INNER JOIN RLT_CITY ct ON b.City_To_Id = ct.PKID
INNER JOIN RLT_TRIP_TYPES tt ON b.Trip_Type_Id = tt.PKID
INNER JOIN RLT_CAR_CATEGORY cc ON b.Car_Category_Id = cc.PKID
WHERE b.Booking_Id = 'CY-310825-75775';

PRINT '=====================================================';
PRINT 'BOOKING RESTORATION COMPLETED SUCCESSFULLY';
PRINT 'Booking ID: CY-310825-75775';
PRINT 'Customer: Ashok Kumar (**********)';
PRINT 'Trip: Bahadurgarh to Jaipur (One Way)';
PRINT 'Total Fare: INR 5500.00 (after discount)';
PRINT 'Advance Paid: INR 1352.00';
PRINT 'Balance: INR 4148.00';
PRINT '=====================================================';
