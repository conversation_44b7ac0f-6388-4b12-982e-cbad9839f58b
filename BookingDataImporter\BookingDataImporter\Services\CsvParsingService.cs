using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BookingDataImporter.Models;
using CsvHelper;
using CsvHelper.Configuration;
using Serilog;

namespace BookingDataImporter.Services
{
    public class CsvParsingService
    {
        private readonly ILogger _logger;

        public CsvParsingService(ILogger logger)
        {
            _logger = logger;
        }

        public async Task<List<BookingCsvRecord>> ParseCsvFileAsync(string filePath)
        {
            var records = new List<BookingCsvRecord>();
            
            try
            {
                using var reader = new StreamReader(filePath);
                using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
                {
                    HasHeaderRecord = true,
                    MissingFieldFound = null, // Ignore missing fields
                    HeaderValidated = null,   // Don't validate headers
                    TrimOptions = TrimOptions.Trim
                });

                await foreach (var record in csv.GetRecordsAsync<BookingCsvRecord>())
                {
                    records.Add(record);
                }

                _logger.Information("Successfully parsed {Count} records from CSV file", records.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error parsing CSV file: {FilePath}", filePath);
                throw;
            }

            return records;
        }
    }

    public class BookingDataMapper
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, int> _cityLookup;
        private readonly Dictionary<string, int> _tripTypeLookup;
        private readonly Dictionary<string, int> _carCategoryLookup;
        private readonly UserManagementService _userManagementService;

        public BookingDataMapper(
            ILogger logger,
            Dictionary<string, int> cityLookup,
            Dictionary<string, int> tripTypeLookup,
            Dictionary<string, int> carCategoryLookup,
            UserManagementService userManagementService)
        {
            _logger = logger;
            _cityLookup = cityLookup;
            _tripTypeLookup = tripTypeLookup;
            _carCategoryLookup = carCategoryLookup;
            _userManagementService = userManagementService;
        }

        public async Task<BookingRecord> MapCsvRecordToBookingRecordAsync(BookingCsvRecord csvRecord, int rowNumber)
        {
            try
            {
                // Get or create user account for this customer
                var userId = await _userManagementService.GetOrCreateUserAsync(csvRecord);

                var booking = new BookingRecord
                {
                    Booking_Id = csvRecord.BookingId?.Trim(),
                    Duration = null, // Not available in CSV
                    Distance = null, // Not available in CSV
                    Basic_Fare = csvRecord.Fare, // Using Fare as Basic_Fare
                    Driver_Charge = 0, // Default value
                    GST = csvRecord.GST,
                    Fare = csvRecord.Fare,
                    GST_Fare = csvRecord.Fare + csvRecord.GST, // Total fare including GST
                    Coupon_Code = null,
                    Coupon_Discount = null,
                    Booking_Date = csvRecord.BookingDate ?? DateTime.Now,
                    PickUp_Address = csvRecord.PickUpAddress?.Trim(),
                    DropOff_Address = csvRecord.DropOffAddress?.Trim(),
                    PickUp_Date = csvRecord.PickUpDate,
                    PickUp_Time = csvRecord.PickUpTime?.Trim(),
                    Name = csvRecord.Name?.Trim(),
                    Mobile_No1 = CleanPhoneNumber(csvRecord.MobileNo1),
                    // Note: Mobile_No2 is not included in stored procedure INSERT
                    Mail_Id = csvRecord.MailId?.Trim(),
                    Mode_Of_Payment_Id = 1, // Default to cash/offline payment
                    Booking_Status_Id = 2, // Set to "Successful" status (PKID 2)
                    Booking_Remark = csvRecord.BookingRemark?.Trim(),
                    Created_By = 1, // Keep as system user for compatibility
                    Booking_Created_By = userId, // Link to actual user account
                    razorpay_payment_id = null,
                    razorpay_order_id = null,
                    razorpay_signature = null,
                    razorpay_status = "2", // Set as paid
                    PickUpAddressLatitude = null,
                    PickUpAddressLongitude = null,
                    CashAmountToPayDriver = 0,
                    PaymentOption = 1,
                    TollCharge = null,
                    PaymentType = "FULL",
                    PartialPaymentAmount = null,
                    RemainingAmountForDriver = null
                };

                // Map City From
                if (!string.IsNullOrWhiteSpace(csvRecord.CityFrom))
                {
                    var cityFromKey = FindCityKey(csvRecord.CityFrom.Trim());
                    if (cityFromKey != null && _cityLookup.ContainsKey(cityFromKey))
                    {
                        booking.City_From_Id = _cityLookup[cityFromKey];
                    }
                    else
                    {
                        throw new InvalidOperationException($"City From '{csvRecord.CityFrom}' not found in lookup table");
                    }
                }
                else
                {
                    throw new InvalidOperationException("City From is required but not provided");
                }

                // Map City To
                if (!string.IsNullOrWhiteSpace(csvRecord.CityTo))
                {
                    var cityToKey = FindCityKey(csvRecord.CityTo.Trim());
                    if (cityToKey != null && _cityLookup.ContainsKey(cityToKey))
                    {
                        booking.City_To_Id = _cityLookup[cityToKey];
                    }
                    else
                    {
                        throw new InvalidOperationException($"City To '{csvRecord.CityTo}' not found in lookup table");
                    }
                }
                else
                {
                    throw new InvalidOperationException("City To is required but not provided");
                }

                // Map Trip Type
                if (!string.IsNullOrWhiteSpace(csvRecord.TripType))
                {
                    var tripTypeKey = FindTripTypeKey(csvRecord.TripType.Trim());
                    if (tripTypeKey != null && _tripTypeLookup.ContainsKey(tripTypeKey))
                    {
                        booking.Trip_Type_Id = _tripTypeLookup[tripTypeKey];
                    }
                    else
                    {
                        throw new InvalidOperationException($"Trip Type '{csvRecord.TripType}' not found in lookup table");
                    }
                }
                else
                {
                    throw new InvalidOperationException("Trip Type is required but not provided");
                }

                // Map Car Category
                if (!string.IsNullOrWhiteSpace(csvRecord.Category))
                {
                    var categoryKey = FindCarCategoryKey(csvRecord.Category.Trim());
                    if (categoryKey != null && _carCategoryLookup.ContainsKey(categoryKey))
                    {
                        booking.Car_Category_Id = _carCategoryLookup[categoryKey];
                    }
                    else
                    {
                        throw new InvalidOperationException($"Car Category '{csvRecord.Category}' not found in lookup table");
                    }
                }
                else
                {
                    throw new InvalidOperationException("Car Category is required but not provided");
                }

                return booking;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error mapping CSV record at row {RowNumber}: {BookingId}", rowNumber, csvRecord.BookingId);
                throw;
            }
        }

        private string FindCityKey(string cityName)
        {
            // Try exact match first
            if (_cityLookup.ContainsKey(cityName))
                return cityName;

            // Try case-insensitive match
            var key = _cityLookup.Keys.FirstOrDefault(k => 
                string.Equals(k, cityName, StringComparison.OrdinalIgnoreCase));
            
            if (key != null)
                return key;

            // Try partial matches for common variations
            key = _cityLookup.Keys.FirstOrDefault(k => 
                k.ToUpperInvariant().Contains(cityName.ToUpperInvariant()) ||
                cityName.ToUpperInvariant().Contains(k.ToUpperInvariant()));

            return key;
        }

        private string FindTripTypeKey(string tripType)
        {
            // Try exact match first
            if (_tripTypeLookup.ContainsKey(tripType))
                return tripType;

            // Try case-insensitive match
            var key = _tripTypeLookup.Keys.FirstOrDefault(k => 
                string.Equals(k, tripType, StringComparison.OrdinalIgnoreCase));
            
            if (key != null)
                return key;

            // Handle common variations
            var normalizedTripType = tripType.ToUpperInvariant().Replace(" ", "");
            key = _tripTypeLookup.Keys.FirstOrDefault(k => 
                k.ToUpperInvariant().Replace(" ", "") == normalizedTripType);

            return key;
        }

        private string FindCarCategoryKey(string category)
        {
            // Try exact match first
            if (_carCategoryLookup.ContainsKey(category))
                return category;

            // Try case-insensitive match
            var key = _carCategoryLookup.Keys.FirstOrDefault(k => 
                string.Equals(k, category, StringComparison.OrdinalIgnoreCase));
            
            if (key != null)
                return key;

            // Handle common variations and partial matches
            var normalizedCategory = category.ToUpperInvariant().Replace(" ", "");
            key = _carCategoryLookup.Keys.FirstOrDefault(k => 
                k.ToUpperInvariant().Replace(" ", "") == normalizedCategory ||
                k.ToUpperInvariant().Contains(normalizedCategory) ||
                normalizedCategory.Contains(k.ToUpperInvariant()));

            return key;
        }

        private string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber) || phoneNumber == "--")
                return null;

            // Remove any non-digit characters except +
            var cleaned = new string(phoneNumber.Where(c => char.IsDigit(c) || c == '+').ToArray());
            
            // Return null if empty after cleaning
            return string.IsNullOrEmpty(cleaned) ? null : cleaned;
        }
    }
}
