using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BookingDataImporter.Models;
using CsvHelper;
using CsvHelper.Configuration;
using Serilog;

namespace BookingDataImporter.Services
{
    public class CsvParsingService
    {
        private readonly ILogger _logger;

        public CsvParsingService(ILogger logger)
        {
            _logger = logger;
        }

        public async Task<List<BookingCsvRecord>> ParseCsvFileAsync(string filePath)
        {
            var records = new List<BookingCsvRecord>();
            
            try
            {
                using var reader = new StreamReader(filePath);
                using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
                {
                    HasHeaderRecord = true,
                    MissingFieldFound = null, // Ignore missing fields
                    HeaderValidated = null,   // Don't validate headers
                    TrimOptions = TrimOptions.Trim
                });

                await foreach (var record in csv.GetRecordsAsync<BookingCsvRecord>())
                {
                    records.Add(record);
                }

                _logger.Information("Successfully parsed {Count} records from CSV file", records.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error parsing CSV file: {FilePath}", filePath);
                throw;
            }

            return records;
        }
    }

    public class BookingDataMapper
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, int> _cityLookup;
        private readonly Dictionary<string, int> _tripTypeLookup;
        private readonly Dictionary<string, int> _carCategoryLookup;
        private readonly UserManagementService _userManagementService;
        private readonly CarModelMappingService _carModelMappingService;
        private readonly TripTypeMappingService _tripTypeMappingService;
        private readonly CityMappingService _cityMappingService;

        public BookingDataMapper(
            ILogger logger,
            Dictionary<string, int> cityLookup,
            Dictionary<string, int> tripTypeLookup,
            Dictionary<string, int> carCategoryLookup,
            UserManagementService userManagementService,
            CarModelMappingService carModelMappingService,
            TripTypeMappingService tripTypeMappingService,
            CityMappingService cityMappingService)
        {
            _logger = logger;
            _cityLookup = cityLookup;
            _tripTypeLookup = tripTypeLookup;
            _carCategoryLookup = carCategoryLookup;
            _userManagementService = userManagementService;
            _carModelMappingService = carModelMappingService;
            _tripTypeMappingService = tripTypeMappingService;
            _cityMappingService = cityMappingService;
        }

        public async Task<BookingRecord> MapCsvRecordToBookingRecordAsync(BookingCsvRecord csvRecord, int rowNumber)
        {
            try
            {
                // Get or create user account for this customer
                var userId = await _userManagementService.GetOrCreateUserAsync(csvRecord);

                var booking = new BookingRecord
                {
                    Booking_Id = csvRecord.BookingId?.Trim(),
                    Duration = null, // Will be calculated if needed
                    Distance = null, // Will be calculated if needed

                    // Use fare values directly from CSV (already calculated)
                    Basic_Fare = csvRecord.Fare, // Use CSV fare as basic fare
                    Driver_Charge = null, // Not specified in CSV
                    GST = csvRecord.GST, // GST amount from CSV
                    Fare = csvRecord.Fare, // Base fare from CSV
                    GST_Fare = csvRecord.GST, // GST amount (separate field)

                    Coupon_Code = null,
                    Coupon_Discount = 0.00m,
                    Booking_Date = csvRecord.BookingDate ?? DateTime.Now,
                    PickUp_Address = csvRecord.PickUpAddress?.Trim(),
                    DropOff_Address = csvRecord.DropOffAddress?.Trim(),
                    PickUp_Date = csvRecord.PickUpDate,
                    PickUp_Time = FormatPickUpTime(csvRecord.PickUpTime), // Format time properly
                    Name = csvRecord.Name?.Trim(),
                    Mobile_No1 = CleanPhoneNumber(csvRecord.MobileNo1),
                    // Note: Mobile_No2 is not included in stored procedure INSERT
                    Mail_Id = string.IsNullOrWhiteSpace(csvRecord.MailId) || csvRecord.MailId == "--" ? null : csvRecord.MailId?.Trim(),
                    Mode_Of_Payment_Id = 4, // Based on your example data (Mode_Of_Payment_Id = 4)
                    Booking_Status_Id = 3, // Based on your example data (Booking_Status_Id = 3 for completed)
                    Created_By = 1, // Keep as system user for compatibility
                    Booking_Created_By = userId, // Link to actual user account
                    razorpay_payment_id = null,
                    razorpay_order_id = null,
                    razorpay_signature = null,
                    razorpay_status = "Paid", // Mark as PAID (status "Paid")
                    PickUpAddressLatitude = null,
                    PickUpAddressLongitude = null,
                    CashAmountToPayDriver = 0, // No cash to driver - fully paid online
                    PaymentOption = 2, // Based on your example data (PaymentOption = 2)
                    TollCharge = null,
                    PaymentType = "FULL", // Mark as FULL payment (matches your example)
                    PartialPaymentAmount = null, // NULL for full payments (matches your example)
                    RemainingAmountForDriver = 0.00m // 0.00 for full payments (matches your example)
                };

                // Map City From with mapping service
                if (!string.IsNullOrWhiteSpace(csvRecord.CityFrom))
                {
                    var mappedCityFrom = _cityMappingService.MapCityName(csvRecord.CityFrom.Trim());
                    if (!string.IsNullOrEmpty(mappedCityFrom) && _cityLookup.ContainsKey(mappedCityFrom))
                    {
                        booking.City_From_Id = _cityLookup[mappedCityFrom];
                        _logger.Debug("Mapped city from '{Original}' -> '{Mapped}' -> ID {Id}",
                            csvRecord.CityFrom, mappedCityFrom, booking.City_From_Id);
                    }
                    else
                    {
                        throw new InvalidOperationException($"City From '{csvRecord.CityFrom}' could not be mapped. Available cities: {string.Join(", ", _cityLookup.Keys)}");
                    }
                }
                else
                {
                    throw new InvalidOperationException("City From is required but not provided");
                }

                // Map City To with mapping service
                if (!string.IsNullOrWhiteSpace(csvRecord.CityTo))
                {
                    var mappedCityTo = _cityMappingService.MapCityName(csvRecord.CityTo.Trim());
                    if (!string.IsNullOrEmpty(mappedCityTo) && _cityLookup.ContainsKey(mappedCityTo))
                    {
                        booking.City_To_Id = _cityLookup[mappedCityTo];
                        _logger.Debug("Mapped city to '{Original}' -> '{Mapped}' -> ID {Id}",
                            csvRecord.CityTo, mappedCityTo, booking.City_To_Id);
                    }
                    else
                    {
                        throw new InvalidOperationException($"City To '{csvRecord.CityTo}' could not be mapped. Available cities: {string.Join(", ", _cityLookup.Keys)}");
                    }
                }
                else
                {
                    throw new InvalidOperationException("City To is required but not provided");
                }

                // Map Trip Type with mapping service
                if (!string.IsNullOrWhiteSpace(csvRecord.TripType))
                {
                    var mappedTripType = _tripTypeMappingService.MapTripType(csvRecord.TripType.Trim());

                    // Check if this trip type should be ignored (ROUND TRIP)
                    if (mappedTripType == "IGNORE")
                    {
                        throw new InvalidOperationException($"Trip Type '{csvRecord.TripType}' is set to be ignored (ROUND TRIP bookings are skipped)");
                    }

                    if (!string.IsNullOrEmpty(mappedTripType) && _tripTypeLookup.ContainsKey(mappedTripType))
                    {
                        booking.Trip_Type_Id = _tripTypeLookup[mappedTripType];
                        _logger.Debug("Mapped trip type '{Original}' -> '{Mapped}' -> ID {Id}",
                            csvRecord.TripType, mappedTripType, booking.Trip_Type_Id);
                    }
                    else
                    {
                        throw new InvalidOperationException($"Trip Type '{csvRecord.TripType}' could not be mapped. Available types: {string.Join(", ", _tripTypeLookup.Keys)}");
                    }
                }
                else
                {
                    throw new InvalidOperationException("Trip Type is required but not provided");
                }

                // Map Car Category with mapping service
                if (!string.IsNullOrWhiteSpace(csvRecord.Category))
                {
                    var mappedCategory = _carModelMappingService.MapCarModelToCategory(csvRecord.Category.Trim());
                    if (!string.IsNullOrEmpty(mappedCategory) && _carCategoryLookup.ContainsKey(mappedCategory))
                    {
                        booking.Car_Category_Id = _carCategoryLookup[mappedCategory];
                        _logger.Debug("Mapped car model '{Original}' -> '{Mapped}' -> ID {Id}",
                            csvRecord.Category, mappedCategory, booking.Car_Category_Id);
                    }
                    else
                    {
                        throw new InvalidOperationException($"Car Category '{csvRecord.Category}' could not be mapped. Available categories: {string.Join(", ", _carCategoryLookup.Keys)}");
                    }
                }
                else
                {
                    throw new InvalidOperationException("Car Category is required but not provided");
                }

                return booking;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error mapping CSV record at row {RowNumber}: {BookingId}", rowNumber, csvRecord.BookingId);
                throw;
            }
        }

        private string FindCityKey(string cityName)
        {
            // Try exact match first
            if (_cityLookup.ContainsKey(cityName))
                return cityName;

            // Try case-insensitive match
            var key = _cityLookup.Keys.FirstOrDefault(k => 
                string.Equals(k, cityName, StringComparison.OrdinalIgnoreCase));
            
            if (key != null)
                return key;

            // Try partial matches for common variations
            key = _cityLookup.Keys.FirstOrDefault(k => 
                k.ToUpperInvariant().Contains(cityName.ToUpperInvariant()) ||
                cityName.ToUpperInvariant().Contains(k.ToUpperInvariant()));

            return key;
        }

        private string FindTripTypeKey(string tripType)
        {
            // Try exact match first
            if (_tripTypeLookup.ContainsKey(tripType))
                return tripType;

            // Try case-insensitive match
            var key = _tripTypeLookup.Keys.FirstOrDefault(k => 
                string.Equals(k, tripType, StringComparison.OrdinalIgnoreCase));
            
            if (key != null)
                return key;

            // Handle common variations
            var normalizedTripType = tripType.ToUpperInvariant().Replace(" ", "");
            key = _tripTypeLookup.Keys.FirstOrDefault(k => 
                k.ToUpperInvariant().Replace(" ", "") == normalizedTripType);

            return key;
        }

        private string FindCarCategoryKey(string category)
        {
            // Try exact match first
            if (_carCategoryLookup.ContainsKey(category))
                return category;

            // Try case-insensitive match
            var key = _carCategoryLookup.Keys.FirstOrDefault(k => 
                string.Equals(k, category, StringComparison.OrdinalIgnoreCase));
            
            if (key != null)
                return key;

            // Handle common variations and partial matches
            var normalizedCategory = category.ToUpperInvariant().Replace(" ", "");
            key = _carCategoryLookup.Keys.FirstOrDefault(k => 
                k.ToUpperInvariant().Replace(" ", "") == normalizedCategory ||
                k.ToUpperInvariant().Contains(normalizedCategory) ||
                normalizedCategory.Contains(k.ToUpperInvariant()));

            return key;
        }

        private string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber) || phoneNumber == "--")
                return null;

            // Remove any non-digit characters except +
            var cleaned = new string(phoneNumber.Where(c => char.IsDigit(c) || c == '+').ToArray());

            // Return null if empty after cleaning
            return string.IsNullOrEmpty(cleaned) ? null : cleaned;
        }

        /// <summary>
        /// Formats pickup time from various formats to standard HH:MM format
        /// Handles formats like "11hrs15", "9:30", "14:00", etc.
        /// </summary>
        private string FormatPickUpTime(string pickUpTime)
        {
            if (string.IsNullOrWhiteSpace(pickUpTime))
                return null;

            var timeStr = pickUpTime.Trim();

            // Handle "11hrs15" format
            if (timeStr.Contains("hrs"))
            {
                try
                {
                    // Extract hours and minutes from "11hrs15" format
                    var parts = timeStr.ToLowerInvariant().Replace("hrs", ":").Replace("hr", ":");

                    // Handle cases like "11hrs15" -> "11:15"
                    if (parts.EndsWith(":"))
                    {
                        parts += "00"; // "11:" -> "11:00"
                    }
                    else
                    {
                        // "11:15" is already correct
                        // But handle "11:5" -> "11:05"
                        var timeParts = parts.Split(':');
                        if (timeParts.Length == 2)
                        {
                            var hours = int.Parse(timeParts[0]);
                            var minutes = int.Parse(timeParts[1]);
                            return $"{hours:D2}:{minutes:D2}";
                        }
                    }

                    return parts;
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to parse time format '{TimeStr}', using original", timeStr);
                    return timeStr;
                }
            }

            // Handle standard time formats like "9:30", "14:00"
            if (timeStr.Contains(":"))
            {
                try
                {
                    var timeParts = timeStr.Split(':');
                    if (timeParts.Length == 2)
                    {
                        var hours = int.Parse(timeParts[0]);
                        var minutes = int.Parse(timeParts[1]);
                        return $"{hours:D2}:{minutes:D2}";
                    }
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to parse standard time format '{TimeStr}', using original", timeStr);
                    return timeStr;
                }
            }

            // Handle numeric only formats like "1130" -> "11:30"
            if (timeStr.All(char.IsDigit) && timeStr.Length == 4)
            {
                try
                {
                    var hours = int.Parse(timeStr.Substring(0, 2));
                    var minutes = int.Parse(timeStr.Substring(2, 2));
                    return $"{hours:D2}:{minutes:D2}";
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to parse numeric time format '{TimeStr}', using original", timeStr);
                    return timeStr;
                }
            }

            // Return original if no pattern matches
            _logger.Debug("Using original time format '{TimeStr}' - no conversion needed", timeStr);
            return timeStr;
        }
    }
}
