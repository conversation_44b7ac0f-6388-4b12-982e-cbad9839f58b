# CabYaari Booking Data Importer

A robust console application for importing booking data from CSV files into the CabYaari database.

## Features

- **CSV Parsing**: Reads booking data from CSV files using CsvHelper library
- **Database Integration**: Connects to SQL Server database and inserts booking records
- **Lookup Table Mapping**: Automatically maps city names, trip types, and car categories to database IDs
- **Error Handling**: Comprehensive error handling with detailed logging
- **Success Tracking**: Marks all imported bookings as successful (Booking_Status_Id = 2)
- **Logging**: Separate log files for general operations and errors
- **Robust Validation**: Validates required fields and data integrity

## Prerequisites

- .NET 9.0 or later
- SQL Server database access
- CSV file with booking data

## Configuration

Update the connection string in `Program.cs`:

```csharp
private static readonly string ConnectionString = "Data Source=your_server;Initial Catalog=CabYaari;user id=your_user;password=your_password;TrustServerCertificate=True;";
```

## CSV File Format

The application expects a CSV file with the following columns:

- Sl No.
- Booking Id
- City From
- City To
- Trip Type
- Category
- Fare
- GST
- Booking Date
- Pick Up Address
- Drop Off Address
- Pick Up Date
- Pick Up Time
- Name
- Mobile No1
- Mobile No2
- Mail Id
- Payment Mode
- Vendor
- Driver
- Car Number
- Status
- Booking Remark

## Usage

### Command Line

```bash
# Use default CSV file (booking_data.csv in application directory)
dotnet run

# Specify custom CSV file path
dotnet run "path/to/your/booking_data.csv"
```

### Build and Run Executable

```bash
# Build the application
dotnet build -c Release

# Run the executable
./bin/Release/net9.0/BookingDataImporter.exe [csv_file_path]
```

## Database Schema

The application inserts data into the `RLT_BOOKING` table with the following key mappings:

- **Cities**: Mapped from `RLT_CITY` table using city names
- **Trip Types**: Mapped from `RLT_TRIP_TYPES` table using trip type names
- **Car Categories**: Mapped from `RLT_CAR_CATEGORY` table using category abbreviations
- **Booking Status**: Set to 2 (Successful) for all imported bookings
- **Payment Status**: Set to "2" (Paid) for razorpay_status

## Logging

The application creates detailed logs in the `Logs` directory:

- `booking_import_YYYYMMDD.log`: General application logs
- `booking_import_errors_YYYYMMDD.log`: Error-specific logs
- `import_errors_YYYYMMDD_HHMMSS.log`: Failed record details

## Error Handling

- **Database Connection**: Tests connection before starting import
- **Lookup Failures**: Logs records that can't be mapped to database IDs
- **Validation Errors**: Validates required fields and data types
- **Insert Failures**: Tracks and logs failed database insertions
- **Partial Success**: Continues processing even if some records fail

## Output

The application provides:

- Real-time console output showing progress
- Summary statistics (total, successful, failed imports)
- Success rate percentage
- Error details for failed records
- Log file locations

## Example Output

```
Starting booking data import...
Processing file: booking_data.csv

=== Import Results ===
Total Records Processed: 63
Successful Imports: 60
Failed Imports: 3
Success Rate: 95.24%

Errors encountered:
  - Row 15: City From 'UNKNOWN_CITY' not found in lookup table
  - Row 28: Validation failed: Mobile number is required
  - Row 45: Car Category 'INVALID_CAR' not found in lookup table

Log files location: D:\path\to\Logs
Import process completed successfully!
```

## Troubleshooting

### Database Connection Issues
- Verify connection string is correct
- Ensure SQL Server is accessible
- Check firewall settings
- Verify user permissions

### CSV Parsing Issues
- Ensure CSV file has proper headers
- Check for encoding issues (use UTF-8)
- Verify column names match expected format

### Lookup Failures
- Check if cities exist in RLT_CITY table
- Verify trip types in RLT_TRIP_TYPES table
- Confirm car categories in RLT_CAR_CATEGORY table

## Dependencies

- **CsvHelper**: CSV file parsing
- **Serilog**: Logging framework
- **Serilog.Sinks.Console**: Console logging
- **Serilog.Sinks.File**: File logging
- **System.Data.SqlClient**: SQL Server connectivity

## Architecture

- **Models**: Data models for CSV records and database entities
- **Services**: 
  - `DatabaseService`: Database operations and lookup data
  - `CsvParsingService`: CSV file parsing
  - `BookingDataMapper`: Maps CSV data to database records
  - `BookingImportService`: Main import orchestration
- **Program**: Entry point and configuration
