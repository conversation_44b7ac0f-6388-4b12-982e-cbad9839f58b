# CabYaari Booking Data Importer

A robust console application for importing booking data from CSV files into the CabYaari database.

## Features

- **CSV Parsing**: Reads booking data from CSV files using CsvHelper library
- **Database Integration**: Connects to SQL Server database and inserts booking records
- **User Account Management**: Automatically creates user accounts for customers or links to existing ones
- **Lookup Table Mapping**: Automatically maps city names, trip types, and car categories to database IDs
- **Error Handling**: Comprehensive error handling with detailed logging
- **Success Tracking**: Marks all imported bookings as successful (Booking_Status_Id = 2)
- **Logging**: Separate log files for general operations and errors
- **Robust Validation**: Validates required fields and data integrity
- **Identity Integration**: Full integration with ASP.NET Identity system

## Prerequisites

- .NET 9.0 or later
- SQL Server database access
- CSV file with booking data

## Configuration

Update the connection string in `Program.cs`:

```csharp
private static readonly string ConnectionString = "Data Source=your_server;Initial Catalog=CabYaari;user id=your_user;password=your_password;TrustServerCertificate=True;";
```

## CSV File Format

The application expects a CSV file with the following columns:

- Sl No.
- Booking Id
- City From
- City To
- Trip Type
- Category
- Fare
- GST
- Booking Date
- Pick Up Address
- Drop Off Address
- Pick Up Date
- Pick Up Time
- Name
- Mobile No1
- Mobile No2
- Mail Id
- Payment Mode
- Vendor
- Driver
- Car Number
- Status
- Booking Remark

## Usage

### Command Line

```bash
# Use default CSV file (booking_data.csv in application directory)
dotnet run

# Specify custom CSV file path
dotnet run "path/to/your/booking_data.csv"
```

### Build and Run Executable

```bash
# Build the application
dotnet build -c Release

# Run the executable
./bin/Release/net9.0/BookingDataImporter.exe [csv_file_path]
```

## Database Schema

The application inserts data into multiple tables with proper relationships:

### User Management
- **User Accounts**: Creates accounts in `Identity.User` table for each unique customer
- **Phone-based Authentication**: Uses phone numbers as usernames for easy login
- **Role Assignment**: Assigns users to "Basic" role automatically
- **Duplicate Prevention**: Checks for existing users by phone number before creating new ones

### Booking Data
- **Cities**: Mapped from `RLT_CITY` table using city names
- **Trip Types**: Mapped from `RLT_TRIP_TYPES` table using trip type names
- **Car Categories**: Mapped from `RLT_CAR_CATEGORY` table using category abbreviations
- **Booking Status**: Set to 2 (Successful) for all imported bookings
- **Payment Status**: Set to "2" (Paid) for razorpay_status
- **User Linking**: `Booking_Created_By` field contains the User ID (GUID) linking to `Identity.User`

### Data Relationships
```
Identity.User (GUID) ←→ RLT_BOOKING.Booking_Created_By
RLT_CITY.PKID ←→ RLT_BOOKING.City_From_Id / City_To_Id
RLT_TRIP_TYPES.PKID ←→ RLT_BOOKING.Trip_Type_Id
RLT_CAR_CATEGORY.PKID ←→ RLT_BOOKING.Car_Category_Id
```

## Logging

The application creates detailed logs in the `Logs` directory:

- `booking_import_YYYYMMDD.log`: General application logs
- `booking_import_errors_YYYYMMDD.log`: Error-specific logs
- `import_errors_YYYYMMDD_HHMMSS.log`: Failed record details

## Error Handling

- **Database Connection**: Tests connection before starting import
- **Lookup Failures**: Logs records that can't be mapped to database IDs
- **Validation Errors**: Validates required fields and data types
- **Insert Failures**: Tracks and logs failed database insertions
- **Partial Success**: Continues processing even if some records fail

## Output

The application provides:

- Real-time console output showing progress
- Summary statistics (total, successful, failed imports)
- Success rate percentage
- Error details for failed records
- Log file locations

## Example Output

```
Starting booking data import...
Processing file: booking_data.csv

=== Import Results ===
Total Records Processed: 63
Successful Imports: 60
Failed Imports: 3
Success Rate: 95.24%

Errors encountered:
  - Row 15: City From 'UNKNOWN_CITY' not found in lookup table
  - Row 28: Validation failed: Mobile number is required
  - Row 45: Car Category 'INVALID_CAR' not found in lookup table

Log files location: D:\path\to\Logs
Import process completed successfully!
```

## Troubleshooting

### Database Connection Issues
- Verify connection string is correct
- Ensure SQL Server is accessible
- Check firewall settings
- Verify user permissions

### CSV Parsing Issues
- Ensure CSV file has proper headers
- Check for encoding issues (use UTF-8)
- Verify column names match expected format

### Lookup Failures
- Check if cities exist in RLT_CITY table
- Verify trip types in RLT_TRIP_TYPES table
- Confirm car categories in RLT_CAR_CATEGORY table

## Dependencies

- **CsvHelper**: CSV file parsing
- **Serilog**: Logging framework
- **Serilog.Sinks.Console**: Console logging
- **Serilog.Sinks.File**: File logging
- **System.Data.SqlClient**: SQL Server connectivity

## User Management Strategy

The application implements a **comprehensive user account creation and linking strategy**:

### 1. **Automatic User Creation**
- For each unique phone number in the CSV, creates a user account in `Identity.User`
- Uses phone number as username for easy authentication
- Splits customer name into FirstName and LastName
- Sets email if provided in CSV data
- Assigns to "Basic" role automatically

### 2. **Duplicate Prevention**
- Maintains an in-memory cache of phone-to-UserID mappings
- Checks database for existing users before creating new ones
- Links multiple bookings from same customer to single user account

### 3. **Booking Linkage**
- `Booking_Created_By` field stores the User ID (GUID)
- `Created_By` remains as system user (1) for compatibility
- Enables customers to view their booking history after login

### 4. **Benefits**
- **Customer Portal**: Users can log in and view their bookings
- **Data Integrity**: No duplicate customer records
- **Future Features**: Enables notifications, loyalty programs, etc.
- **Analytics**: Better customer behavior tracking

## Architecture

- **Models**: Data models for CSV records and database entities
- **Services**:
  - `DatabaseService`: Database operations and lookup data
  - `UserManagementService`: User account creation and management
  - `CsvParsingService`: CSV file parsing
  - `BookingDataMapper`: Maps CSV data to database records
  - `BookingImportService`: Main import orchestration
- **Program**: Entry point and configuration
