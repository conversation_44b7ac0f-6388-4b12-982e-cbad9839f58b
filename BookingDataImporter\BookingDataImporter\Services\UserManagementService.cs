using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using BookingDataImporter.Models;
using Serilog;

namespace BookingDataImporter.Services
{
    public class UserManagementService
    {
        private readonly string _connectionString;
        private readonly ILogger _logger;
        private readonly Dictionary<string, string> _phoneToUserIdCache;

        public UserManagementService(string connectionString, ILogger logger)
        {
            _connectionString = connectionString;
            _logger = logger;
            _phoneToUserIdCache = new Dictionary<string, string>();
        }

        /// <summary>
        /// Gets or creates a user account for the given customer data
        /// Returns the User ID (GUID) to be used in booking records
        /// </summary>
        public async Task<string> GetOrCreateUserAsync(BookingCsvRecord csvRecord)
        {
            try
            {
                var phoneNumber = CleanPhoneNumber(csvRecord.MobileNo1);
                if (string.IsNullOrEmpty(phoneNumber))
                {
                    throw new InvalidOperationException("Valid phone number is required for user creation");
                }

                // Check cache first
                if (_phoneToUserIdCache.ContainsKey(phoneNumber))
                {
                    return _phoneToUserIdCache[phoneNumber];
                }

                // Check if user already exists
                var existingUserId = await FindExistingUserAsync(phoneNumber);
                if (!string.IsNullOrEmpty(existingUserId))
                {
                    _phoneToUserIdCache[phoneNumber] = existingUserId;
                    _logger.Debug("Found existing user {UserId} for phone {Phone}", existingUserId, phoneNumber);
                    return existingUserId;
                }

                // Create new user
                var newUserId = await CreateNewUserAsync(csvRecord, phoneNumber);
                _phoneToUserIdCache[phoneNumber] = newUserId;
                _logger.Information("Created new user {UserId} for {Name} ({Phone})", newUserId, csvRecord.Name, phoneNumber);
                
                return newUserId;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting or creating user for {Name} ({Phone})", csvRecord.Name, csvRecord.MobileNo1);
                throw;
            }
        }

        private async Task<string> FindExistingUserAsync(string phoneNumber)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT Id 
                    FROM [Identity].[User] 
                    WHERE PhoneNumber = @PhoneNumber 
                       OR UserName = @PhoneNumber";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@PhoneNumber", phoneNumber);

                var result = await command.ExecuteScalarAsync();
                return result?.ToString();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error finding existing user for phone {Phone}", phoneNumber);
                return null;
            }
        }

        private async Task<string> CreateNewUserAsync(BookingCsvRecord csvRecord, string phoneNumber)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var userId = Guid.NewGuid().ToString();
                var securityStamp = Guid.NewGuid().ToString();
                var concurrencyStamp = Guid.NewGuid().ToString();

                // Split name into first and last name
                var nameParts = (csvRecord.Name?.Trim() ?? "").Split(' ', StringSplitOptions.RemoveEmptyEntries);
                var firstName = nameParts.Length > 0 ? nameParts[0] : "Customer";
                var lastName = nameParts.Length > 1 ? string.Join(" ", nameParts.Skip(1)) : "";

                var query = @"
                    INSERT INTO [Identity].[User] (
                        Id, UserName, NormalizedUserName, Email, NormalizedEmail, 
                        EmailConfirmed, PasswordHash, SecurityStamp, ConcurrencyStamp,
                        PhoneNumber, PhoneNumberConfirmed, TwoFactorEnabled, LockoutEnabled,
                        AccessFailedCount, FirstName, LastName, UserType, 
                        OTPExpireTimeInMinute, CurrentOTPNumber, OTPGeneratedDate
                    ) VALUES (
                        @Id, @UserName, @NormalizedUserName, @Email, @NormalizedEmail,
                        @EmailConfirmed, @PasswordHash, @SecurityStamp, @ConcurrencyStamp,
                        @PhoneNumber, @PhoneNumberConfirmed, @TwoFactorEnabled, @LockoutEnabled,
                        @AccessFailedCount, @FirstName, @LastName, @UserType,
                        @OTPExpireTimeInMinute, @CurrentOTPNumber, @OTPGeneratedDate
                    )";

                using var command = new SqlCommand(query, connection);
                
                command.Parameters.AddWithValue("@Id", userId);
                command.Parameters.AddWithValue("@UserName", phoneNumber);
                command.Parameters.AddWithValue("@NormalizedUserName", phoneNumber.ToUpperInvariant());
                command.Parameters.AddWithValue("@Email", csvRecord.MailId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@NormalizedEmail", 
                    string.IsNullOrEmpty(csvRecord.MailId) ? (object)DBNull.Value : csvRecord.MailId.ToUpperInvariant());
                command.Parameters.AddWithValue("@EmailConfirmed", !string.IsNullOrEmpty(csvRecord.MailId));
                command.Parameters.AddWithValue("@PasswordHash", DBNull.Value); // No password for imported users
                command.Parameters.AddWithValue("@SecurityStamp", securityStamp);
                command.Parameters.AddWithValue("@ConcurrencyStamp", concurrencyStamp);
                command.Parameters.AddWithValue("@PhoneNumber", phoneNumber);
                command.Parameters.AddWithValue("@PhoneNumberConfirmed", true); // Assume confirmed for imported data
                command.Parameters.AddWithValue("@TwoFactorEnabled", false);
                command.Parameters.AddWithValue("@LockoutEnabled", false);
                command.Parameters.AddWithValue("@AccessFailedCount", 0);
                command.Parameters.AddWithValue("@FirstName", firstName);
                command.Parameters.AddWithValue("@LastName", lastName);
                command.Parameters.AddWithValue("@UserType", 1); // WebUser type
                command.Parameters.AddWithValue("@OTPExpireTimeInMinute", 10);
                command.Parameters.AddWithValue("@CurrentOTPNumber", DBNull.Value);
                command.Parameters.AddWithValue("@OTPGeneratedDate", DBNull.Value);

                await command.ExecuteNonQueryAsync();

                // Add user to Basic role
                await AddUserToRoleAsync(connection, userId, "Basic");

                return userId;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating new user for {Name} ({Phone})", csvRecord.Name, phoneNumber);
                throw;
            }
        }

        private async Task AddUserToRoleAsync(SqlConnection connection, string userId, string roleName)
        {
            try
            {
                // First get the role ID
                var getRoleQuery = "SELECT Id FROM [Identity].[Role] WHERE Name = @RoleName";
                using var getRoleCommand = new SqlCommand(getRoleQuery, connection);
                getRoleCommand.Parameters.AddWithValue("@RoleName", roleName);
                
                var roleId = await getRoleCommand.ExecuteScalarAsync();
                if (roleId == null)
                {
                    _logger.Warning("Role {RoleName} not found, skipping role assignment", roleName);
                    return;
                }

                // Add user to role
                var addRoleQuery = @"
                    INSERT INTO [Identity].[UserRoles] (UserId, RoleId) 
                    VALUES (@UserId, @RoleId)";
                
                using var addRoleCommand = new SqlCommand(addRoleQuery, connection);
                addRoleCommand.Parameters.AddWithValue("@UserId", userId);
                addRoleCommand.Parameters.AddWithValue("@RoleId", roleId.ToString());
                
                await addRoleCommand.ExecuteNonQueryAsync();
                _logger.Debug("Added user {UserId} to role {RoleName}", userId, roleName);
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Failed to add user {UserId} to role {RoleName}", userId, roleName);
                // Don't throw - role assignment failure shouldn't stop user creation
            }
        }

        private string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber) || phoneNumber == "--")
                return null;

            // Remove any non-digit characters except +
            var cleaned = new string(phoneNumber.Where(c => char.IsDigit(c) || c == '+').ToArray());
            
            // Ensure it starts with country code for India if it's a 10-digit number
            if (cleaned.Length == 10 && !cleaned.StartsWith("+"))
            {
                cleaned = "+91" + cleaned;
            }
            else if (cleaned.Length == 10 && cleaned.StartsWith("91"))
            {
                cleaned = "+" + cleaned;
            }
            
            return string.IsNullOrEmpty(cleaned) ? null : cleaned;
        }

        public void ClearCache()
        {
            _phoneToUserIdCache.Clear();
        }
    }
}
