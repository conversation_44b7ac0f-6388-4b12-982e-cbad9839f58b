using System;
using System.Collections.Generic;

namespace BookingDataImporter.Models
{
    public class City
    {
        public int PKID { get; set; }
        public string City_Name { get; set; }
        public string City_Abbr { get; set; }
        public bool Is_Active { get; set; }
    }

    public class TripType
    {
        public int PKID { get; set; }
        public string Trip_Type { get; set; }
        public bool Is_Active { get; set; }
    }

    public class CarCategory
    {
        public int PKID { get; set; }
        public string Car_Category_Name { get; set; }
        public string Car_Category_Abbr { get; set; }
        public decimal? Per_KM_Fare { get; set; }
        public decimal? Base_Fare { get; set; }
        public int? Capacity { get; set; }
        public string Features { get; set; }
        public bool? Is_Active { get; set; }
    }

    public class BookingStatus
    {
        public int PKID { get; set; }
        public string Status { get; set; }
        public bool? IsActive { get; set; }
    }

    public class BookingRecord
    {
        public string Booking_Id { get; set; }
        public int City_From_Id { get; set; }
        public int City_To_Id { get; set; }
        public int Trip_Type_Id { get; set; }
        public int Car_Category_Id { get; set; }
        public string Duration { get; set; }
        public decimal? Distance { get; set; }
        public decimal? Basic_Fare { get; set; }
        public decimal? Driver_Charge { get; set; }
        public decimal? GST { get; set; }
        public decimal? Fare { get; set; }
        public decimal? GST_Fare { get; set; }
        public string Coupon_Code { get; set; }
        public decimal? Coupon_Discount { get; set; }
        public DateTime? Booking_Date { get; set; }
        public string PickUp_Address { get; set; }
        public string DropOff_Address { get; set; }
        public DateTime? PickUp_Date { get; set; }
        public string PickUp_Time { get; set; }
        public string Name { get; set; }
        public string Mobile_No1 { get; set; }
        public string Mobile_No2 { get; set; }
        public string Mail_Id { get; set; }
        public int? Mode_Of_Payment_Id { get; set; }
        public int? Booking_Status_Id { get; set; }
        public string Booking_Remark { get; set; }
        public int? Created_By { get; set; }
        public string Booking_Created_By { get; set; }
        public string razorpay_payment_id { get; set; }
        public string razorpay_order_id { get; set; }
        public string razorpay_signature { get; set; }
        public string razorpay_status { get; set; }
        public string PickUpAddressLatitude { get; set; }
        public string PickUpAddressLongitude { get; set; }
        public decimal? CashAmountToPayDriver { get; set; }
        public int? PaymentOption { get; set; }
        public decimal? TollCharge { get; set; }
        public string PaymentType { get; set; }
        public decimal? PartialPaymentAmount { get; set; }
        public decimal? RemainingAmountForDriver { get; set; }
    }

    public class ImportResult
    {
        public int TotalRecords { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public List<string> ErrorMessages { get; set; } = new List<string>();
    }

    public class ErrorRecord
    {
        public int RowNumber { get; set; }
        public string BookingId { get; set; }
        public string ErrorMessage { get; set; }
        public string RawData { get; set; }
        public DateTime ErrorTime { get; set; }
    }
}
