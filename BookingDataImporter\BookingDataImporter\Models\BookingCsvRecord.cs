using CsvHelper.Configuration.Attributes;
using System;

namespace BookingDataImporter.Models
{
    public class BookingCsvRecord
    {
        [Name("Sl No.")]
        public int SlNo { get; set; }

        [Name("Booking Id")]
        public string BookingId { get; set; }

        [Name("City From")]
        public string CityFrom { get; set; }

        [Name("City To")]
        public string CityTo { get; set; }

        [Name("Trip Type")]
        public string TripType { get; set; }

        [Name("Category")]
        public string Category { get; set; }

        [Name("Fare")]
        public decimal Fare { get; set; }

        [Name("GST")]
        public decimal GST { get; set; }

        [Name("Booking Date")]
        public string BookingDateString { get; set; }

        [Name("Pick Up Address")]
        public string PickUpAddress { get; set; }

        [Name("Drop Off Address")]
        public string DropOffAddress { get; set; }

        [Name("Pick Up Date")]
        public string PickUpDateString { get; set; }

        [Name("Pick Up Time")]
        public string PickUpTime { get; set; }

        [Name("Name")]
        public string Name { get; set; }

        [Name("Mobile No1")]
        public string MobileNo1 { get; set; }

        [Name("Mobile No2")]
        public string MobileNo2 { get; set; }

        [Name("Mail Id")]
        public string MailId { get; set; }

        [Name("Payment Mode")]
        public string PaymentMode { get; set; }

        [Name("Vendor")]
        public string Vendor { get; set; }

        [Name("Driver")]
        public string Driver { get; set; }

        [Name("Car Number")]
        public string CarNumber { get; set; }

        [Name("Status")]
        public string Status { get; set; }

        [Name("Booking Remark")]
        public string BookingRemark { get; set; }

        // Computed properties for date parsing with DD/MM/YYYY format support
        public DateTime? BookingDate
        {
            get
            {
                return ParseDateString(BookingDateString);
            }
        }

        public DateTime? PickUpDate
        {
            get
            {
                return ParseDateString(PickUpDateString);
            }
        }

        /// <summary>
        /// Parses date string supporting multiple formats including DD/MM/YYYY
        /// </summary>
        private DateTime? ParseDateString(string dateString)
        {
            if (string.IsNullOrWhiteSpace(dateString))
                return null;

            // Try multiple date formats commonly used in CSV files
            string[] formats = {
                "dd/MM/yyyy",    // 13/06/2025
                "d/MM/yyyy",     // 6/06/2025
                "dd/M/yyyy",     // 13/6/2025
                "d/M/yyyy",      // 6/6/2025
                "MM/dd/yyyy",    // 06/13/2025 (US format)
                "M/dd/yyyy",     // 6/13/2025 (US format)
                "M/d/yyyy",      // 6/6/2025 (US format)
                "yyyy-MM-dd",    // 2025-06-13 (ISO format)
                "yyyy/MM/dd",    // 2025/06/13
                "dd-MM-yyyy",    // 13-06-2025
                "dd.MM.yyyy"     // 13.06.2025
            };

            // Try parsing with specific formats first (DD/MM/YYYY priority)
            foreach (string format in formats)
            {
                if (DateTime.TryParseExact(dateString.Trim(), format,
                    System.Globalization.CultureInfo.InvariantCulture,
                    System.Globalization.DateTimeStyles.None, out DateTime result))
                {
                    return result;
                }
            }

            // Fallback to default parsing
            if (DateTime.TryParse(dateString, out DateTime fallbackResult))
                return fallbackResult;

            return null;
        }
    }
}
