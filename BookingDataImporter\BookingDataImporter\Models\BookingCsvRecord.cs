using CsvHelper.Configuration.Attributes;
using System;

namespace BookingDataImporter.Models
{
    public class BookingCsvRecord
    {
        [Name("Sl No.")]
        public int SlNo { get; set; }

        [Name("Booking Id")]
        public string BookingId { get; set; }

        [Name("City From")]
        public string CityFrom { get; set; }

        [Name("City To")]
        public string CityTo { get; set; }

        [Name("Trip Type")]
        public string TripType { get; set; }

        [Name("Category")]
        public string Category { get; set; }

        [Name("Fare")]
        public decimal Fare { get; set; }

        [Name("GST")]
        public decimal GST { get; set; }

        [Name("Booking Date")]
        public string BookingDateString { get; set; }

        [Name("Pick Up Address")]
        public string PickUpAddress { get; set; }

        [Name("Drop Off Address")]
        public string DropOffAddress { get; set; }

        [Name("Pick Up Date")]
        public string PickUpDateString { get; set; }

        [Name("Pick Up Time")]
        public string PickUpTime { get; set; }

        [Name("Name")]
        public string Name { get; set; }

        [Name("Mobile No1")]
        public string MobileNo1 { get; set; }

        [Name("Mobile No2")]
        public string MobileNo2 { get; set; }

        [Name("Mail Id")]
        public string MailId { get; set; }

        [Name("Payment Mode")]
        public string PaymentMode { get; set; }

        [Name("Vendor")]
        public string Vendor { get; set; }

        [Name("Driver")]
        public string Driver { get; set; }

        [Name("Car Number")]
        public string CarNumber { get; set; }

        [Name("Status")]
        public string Status { get; set; }

        [Name("Booking Remark")]
        public string BookingRemark { get; set; }

        // Computed properties for date parsing
        public DateTime? BookingDate
        {
            get
            {
                if (DateTime.TryParse(BookingDateString, out DateTime result))
                    return result;
                return null;
            }
        }

        public DateTime? PickUpDate
        {
            get
            {
                if (DateTime.TryParse(PickUpDateString, out DateTime result))
                    return result;
                return null;
            }
        }
    }
}
