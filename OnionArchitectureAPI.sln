﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36310.24 d17.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WebApi", "WebApi\WebApi.csproj", "{18DF5F63-59DF-4614-8B0C-EF8D55C88355}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{EF716D02-8493-4E06-AB4A-90BC1FD3816E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{9CC5C9E9-67D7-4EDC-8F4E-480DF0E2BD92}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Domain", "Domain\Domain.csproj", "{329749BC-4857-48DC-A9DD-725C3B4C96B8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Infrastructure.Identity", "Infrastructure.Identity\Infrastructure.Identity.csproj", "{02D59055-B25F-471E-AA39-FBEC2D1FC6D7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Infrastructure.Persistence", "Infrastructure.Persistence\Infrastructure.Persistence.csproj", "{C679713A-342B-488E-A076-177D1EBF93FA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Infrastructure.Shared", "Infrastructure.Shared\Infrastructure.Shared.csproj", "{BA9187B0-EF8A-415B-A5B7-69A9B0C45D1E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application", "Application\Application.csproj", "{851AC120-6EC2-455F-9D5F-6B6EA1C68842}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BookingDataImporter", "BookingDataImporter\BookingDataImporter\BookingDataImporter.csproj", "{54EE6B73-A485-7FEB-2732-87ABF4BCD623}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{18DF5F63-59DF-4614-8B0C-EF8D55C88355}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{18DF5F63-59DF-4614-8B0C-EF8D55C88355}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{18DF5F63-59DF-4614-8B0C-EF8D55C88355}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{18DF5F63-59DF-4614-8B0C-EF8D55C88355}.Release|Any CPU.Build.0 = Release|Any CPU
		{329749BC-4857-48DC-A9DD-725C3B4C96B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{329749BC-4857-48DC-A9DD-725C3B4C96B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{329749BC-4857-48DC-A9DD-725C3B4C96B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{329749BC-4857-48DC-A9DD-725C3B4C96B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{02D59055-B25F-471E-AA39-FBEC2D1FC6D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{02D59055-B25F-471E-AA39-FBEC2D1FC6D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{02D59055-B25F-471E-AA39-FBEC2D1FC6D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{02D59055-B25F-471E-AA39-FBEC2D1FC6D7}.Release|Any CPU.Build.0 = Release|Any CPU
		{C679713A-342B-488E-A076-177D1EBF93FA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C679713A-342B-488E-A076-177D1EBF93FA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C679713A-342B-488E-A076-177D1EBF93FA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C679713A-342B-488E-A076-177D1EBF93FA}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA9187B0-EF8A-415B-A5B7-69A9B0C45D1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA9187B0-EF8A-415B-A5B7-69A9B0C45D1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA9187B0-EF8A-415B-A5B7-69A9B0C45D1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA9187B0-EF8A-415B-A5B7-69A9B0C45D1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{851AC120-6EC2-455F-9D5F-6B6EA1C68842}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{851AC120-6EC2-455F-9D5F-6B6EA1C68842}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{851AC120-6EC2-455F-9D5F-6B6EA1C68842}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{851AC120-6EC2-455F-9D5F-6B6EA1C68842}.Release|Any CPU.Build.0 = Release|Any CPU
		{54EE6B73-A485-7FEB-2732-87ABF4BCD623}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{54EE6B73-A485-7FEB-2732-87ABF4BCD623}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{54EE6B73-A485-7FEB-2732-87ABF4BCD623}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{54EE6B73-A485-7FEB-2732-87ABF4BCD623}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{329749BC-4857-48DC-A9DD-725C3B4C96B8} = {9CC5C9E9-67D7-4EDC-8F4E-480DF0E2BD92}
		{02D59055-B25F-471E-AA39-FBEC2D1FC6D7} = {EF716D02-8493-4E06-AB4A-90BC1FD3816E}
		{C679713A-342B-488E-A076-177D1EBF93FA} = {EF716D02-8493-4E06-AB4A-90BC1FD3816E}
		{BA9187B0-EF8A-415B-A5B7-69A9B0C45D1E} = {EF716D02-8493-4E06-AB4A-90BC1FD3816E}
		{851AC120-6EC2-455F-9D5F-6B6EA1C68842} = {9CC5C9E9-67D7-4EDC-8F4E-480DF0E2BD92}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F148D7D2-1813-4CBF-9FBF-B38078C8A213}
	EndGlobalSection
EndGlobal
