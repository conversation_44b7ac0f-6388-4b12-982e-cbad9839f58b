using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BookingDataImporter.Models;
using Serilog;

namespace BookingDataImporter.Services
{
    public class BookingImportService
    {
        private readonly DatabaseService _databaseService;
        private readonly CsvParsingService _csvParsingService;
        private readonly ILogger _logger;
        private readonly ILogger _errorLogger;

        public BookingImportService(
            DatabaseService databaseService,
            CsvParsingService csvParsingService,
            ILogger logger,
            ILogger errorLogger)
        {
            _databaseService = databaseService;
            _csvParsingService = csvParsingService;
            _logger = logger;
            _errorLogger = errorLogger;
        }

        public async Task<ImportResult> ImportBookingsFromCsvAsync(string csvFilePath)
        {
            var result = new ImportResult();
            var errorRecords = new List<ErrorRecord>();

            try
            {
                _logger.Information("Starting booking import from CSV file: {FilePath}", csvFilePath);

                // Load lookup data from database
                _logger.Information("Loading lookup data from database...");
                var cityLookup = await _databaseService.GetCityLookupAsync();
                var tripTypeLookup = await _databaseService.GetTripTypeLookupAsync();
                var carCategoryLookup = await _databaseService.GetCarCategoryLookupAsync();

                // Log available lookup data for debugging
                _logger.Information("Available cities: {Cities}", string.Join(", ", cityLookup.Keys));
                _logger.Information("Available trip types: {TripTypes}", string.Join(", ", tripTypeLookup.Keys));
                _logger.Information("Available car categories: {Categories}", string.Join(", ", carCategoryLookup.Keys));

                // Parse CSV file
                _logger.Information("Parsing CSV file...");
                var csvRecords = await _csvParsingService.ParseCsvFileAsync(csvFilePath);
                result.TotalRecords = csvRecords.Count;

                // Create data mapper
                var dataMapper = new BookingDataMapper(_logger, cityLookup, tripTypeLookup, carCategoryLookup);

                // Process each record
                _logger.Information("Processing {Count} booking records...", csvRecords.Count);
                
                for (int i = 0; i < csvRecords.Count; i++)
                {
                    var csvRecord = csvRecords[i];
                    var rowNumber = i + 2; // +2 because CSV has header row and we're 0-indexed

                    try
                    {
                        _logger.Debug("Processing row {RowNumber}: Booking ID {BookingId}", rowNumber, csvRecord.BookingId);

                        // Skip empty rows
                        if (string.IsNullOrWhiteSpace(csvRecord.BookingId))
                        {
                            _logger.Warning("Skipping empty row {RowNumber}", rowNumber);
                            continue;
                        }

                        // Map CSV record to booking record
                        var bookingRecord = dataMapper.MapCsvRecordToBookingRecord(csvRecord, rowNumber);

                        // Validate required fields
                        ValidateBookingRecord(bookingRecord, rowNumber);

                        // Insert booking into database
                        var insertSuccess = await _databaseService.InsertBookingAsync(bookingRecord);

                        if (insertSuccess)
                        {
                            result.SuccessfulImports++;
                            _logger.Debug("Successfully imported booking {BookingId} at row {RowNumber}", 
                                bookingRecord.Booking_Id, rowNumber);
                        }
                        else
                        {
                            result.FailedImports++;
                            var errorMessage = $"Failed to insert booking into database";
                            result.ErrorMessages.Add($"Row {rowNumber}: {errorMessage}");
                            
                            errorRecords.Add(new ErrorRecord
                            {
                                RowNumber = rowNumber,
                                BookingId = csvRecord.BookingId,
                                ErrorMessage = errorMessage,
                                RawData = SerializeCsvRecord(csvRecord),
                                ErrorTime = DateTime.Now
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        result.FailedImports++;
                        var errorMessage = $"Error processing record: {ex.Message}";
                        result.ErrorMessages.Add($"Row {rowNumber}: {errorMessage}");
                        
                        _logger.Error(ex, "Error processing row {RowNumber}: Booking ID {BookingId}", 
                            rowNumber, csvRecord.BookingId);

                        errorRecords.Add(new ErrorRecord
                        {
                            RowNumber = rowNumber,
                            BookingId = csvRecord.BookingId,
                            ErrorMessage = errorMessage,
                            RawData = SerializeCsvRecord(csvRecord),
                            ErrorTime = DateTime.Now
                        });
                    }
                }

                // Log error records to separate file
                if (errorRecords.Any())
                {
                    await LogErrorRecordsAsync(errorRecords);
                }

                _logger.Information("Import completed. Total: {Total}, Success: {Success}, Failed: {Failed}", 
                    result.TotalRecords, result.SuccessfulImports, result.FailedImports);

            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Fatal error during booking import");
                result.ErrorMessages.Add($"Fatal error: {ex.Message}");
                throw;
            }

            return result;
        }

        private void ValidateBookingRecord(BookingRecord booking, int rowNumber)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(booking.Booking_Id))
                errors.Add("Booking ID is required");

            if (booking.City_From_Id <= 0)
                errors.Add("Valid City From is required");

            if (booking.City_To_Id <= 0)
                errors.Add("Valid City To is required");

            if (booking.Trip_Type_Id <= 0)
                errors.Add("Valid Trip Type is required");

            if (booking.Car_Category_Id <= 0)
                errors.Add("Valid Car Category is required");

            if (string.IsNullOrWhiteSpace(booking.Name))
                errors.Add("Customer name is required");

            if (string.IsNullOrWhiteSpace(booking.Mobile_No1))
                errors.Add("Mobile number is required");

            if (errors.Any())
            {
                throw new InvalidOperationException($"Validation failed: {string.Join(", ", errors)}");
            }
        }

        private string SerializeCsvRecord(BookingCsvRecord record)
        {
            try
            {
                return $"BookingId: {record.BookingId}, " +
                       $"CityFrom: {record.CityFrom}, " +
                       $"CityTo: {record.CityTo}, " +
                       $"TripType: {record.TripType}, " +
                       $"Category: {record.Category}, " +
                       $"Name: {record.Name}, " +
                       $"Mobile: {record.MobileNo1}, " +
                       $"Fare: {record.Fare}, " +
                       $"GST: {record.GST}";
            }
            catch
            {
                return "Unable to serialize record data";
            }
        }

        private async Task LogErrorRecordsAsync(List<ErrorRecord> errorRecords)
        {
            try
            {
                var errorLogPath = Path.Combine(Directory.GetCurrentDirectory(), "Logs", $"import_errors_{DateTime.Now:yyyyMMdd_HHmmss}.log");
                
                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(errorLogPath));

                var errorLines = errorRecords.Select(error => 
                    $"[{error.ErrorTime:yyyy-MM-dd HH:mm:ss}] Row {error.RowNumber} - BookingId: {error.BookingId} - Error: {error.ErrorMessage} - Data: {error.RawData}");

                await File.WriteAllLinesAsync(errorLogPath, errorLines);
                
                _logger.Information("Error records logged to: {ErrorLogPath}", errorLogPath);
                
                // Also log to the error logger
                foreach (var error in errorRecords)
                {
                    _errorLogger.Error("Import Error - Row {RowNumber}: {BookingId} - {ErrorMessage} - {RawData}", 
                        error.RowNumber, error.BookingId, error.ErrorMessage, error.RawData);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to write error log file");
            }
        }
    }
}
