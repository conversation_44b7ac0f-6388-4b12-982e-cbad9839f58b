﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BookingDataImporter.Services;
using Serilog;

namespace BookingDataImporter
{
    internal class Program
    {
        private static readonly string ConnectionString = "Data Source=localhost,1433;Initial Catalog=CabYaari;user id=cabyaari_user;password=********************************;TrustServerCertificate=True;";

        static async Task Main(string[] args)
        {
            // Configure logging
            var logDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Logs");
            Directory.CreateDirectory(logDirectory);

            var mainLogger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console()
                .WriteTo.File(Path.Combine(logDirectory, $"booking_import_{DateTime.Now:yyyyMMdd}.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30)
                .CreateLogger();

            var errorLogger = new LoggerConfiguration()
                .MinimumLevel.Error()
                .WriteTo.File(Path.Combine(logDirectory, $"booking_import_errors_{DateTime.Now:yyyyMMdd}.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30)
                .CreateLogger();

            try
            {
                mainLogger.Information("=== CabYaari Booking Data Importer Started ===");
                mainLogger.Information("Application started at: {StartTime}", DateTime.Now);

                // Get CSV file path
                string csvFilePath;
                if (args.Length > 0)
                {
                    csvFilePath = args[0];
                }
                else
                {
                    csvFilePath = Path.Combine(Directory.GetCurrentDirectory(), "booking_data.csv");
                }

                // Validate CSV file exists
                if (!File.Exists(csvFilePath))
                {
                    mainLogger.Error("CSV file not found: {FilePath}", csvFilePath);
                    Console.WriteLine($"Error: CSV file not found at {csvFilePath}");
                    Console.WriteLine("Usage: BookingDataImporter.exe [path_to_csv_file]");
                    Environment.Exit(1);
                    return;
                }

                mainLogger.Information("Using CSV file: {FilePath}", csvFilePath);

                // Initialize services
                var databaseService = new DatabaseService(ConnectionString, mainLogger);
                var csvParsingService = new CsvParsingService(mainLogger);
                var bookingImportService = new BookingImportService(
                    databaseService,
                    csvParsingService,
                    mainLogger,
                    errorLogger);

                // Test database connection
                mainLogger.Information("Testing database connection...");
                try
                {
                    var testCities = await databaseService.GetCityLookupAsync();
                    mainLogger.Information("Database connection successful. Found {CityCount} cities.", testCities.Count);
                }
                catch (Exception ex)
                {
                    mainLogger.Error(ex, "Database connection failed");
                    Console.WriteLine("Error: Unable to connect to database. Please check connection string and database availability.");
                    Environment.Exit(1);
                    return;
                }

                // Start import process
                mainLogger.Information("Starting booking import process...");
                Console.WriteLine("Starting booking data import...");
                Console.WriteLine($"Processing file: {csvFilePath}");

                var result = await bookingImportService.ImportBookingsFromCsvAsync(csvFilePath);

                // Display results
                Console.WriteLine("\n=== Import Results ===");
                Console.WriteLine($"Total Records Processed: {result.TotalRecords}");
                Console.WriteLine($"Successful Imports: {result.SuccessfulImports}");
                Console.WriteLine($"Failed Imports: {result.FailedImports}");
                Console.WriteLine($"Success Rate: {(result.TotalRecords > 0 ? (double)result.SuccessfulImports / result.TotalRecords * 100 : 0):F2}%");

                mainLogger.Information("Import completed. Total: {Total}, Success: {Success}, Failed: {Failed}, Success Rate: {SuccessRate:F2}%",
                    result.TotalRecords, result.SuccessfulImports, result.FailedImports,
                    result.TotalRecords > 0 ? (double)result.SuccessfulImports / result.TotalRecords * 100 : 0);

                if (result.FailedImports > 0)
                {
                    Console.WriteLine("\nErrors encountered:");
                    foreach (var error in result.ErrorMessages.Take(10)) // Show first 10 errors
                    {
                        Console.WriteLine($"  - {error}");
                    }

                    if (result.ErrorMessages.Count > 10)
                    {
                        Console.WriteLine($"  ... and {result.ErrorMessages.Count - 10} more errors. Check log files for details.");
                    }

                    Console.WriteLine($"\nDetailed error logs saved to: {logDirectory}");
                }

                Console.WriteLine($"\nLog files location: {logDirectory}");
                Console.WriteLine("Import process completed successfully!");

            }
            catch (Exception ex)
            {
                mainLogger.Error(ex, "Fatal error in main application");
                Console.WriteLine($"Fatal error: {ex.Message}");
                Console.WriteLine("Check log files for detailed error information.");
                Environment.Exit(1);
            }
            finally
            {
                mainLogger.Information("=== CabYaari Booking Data Importer Finished ===");
                mainLogger.Dispose();
                errorLogger.Dispose();
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
