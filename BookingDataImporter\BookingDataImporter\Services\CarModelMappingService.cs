using System;
using System.Collections.Generic;
using Serilog;

namespace BookingDataImporter.Services
{
    public class CarModelMappingService
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, string> _carModelToCategoryMapping;

        public CarModelMappingService(ILogger logger)
        {
            _logger = logger;
            _carModelToCategoryMapping = InitializeCarModelMapping();
        }

        /// <summary>
        /// Maps car model names from CSV to car category abbreviations used in database
        /// </summary>
        private Dictionary<string, string> InitializeCarModelMapping()
        {
            var mapping = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                // SUV Premium Category (typically Innova Crysta)
                ["INNOVA CRYSTA"] = "SUV Premium",
                ["INNOVA"] = "SUV Premium",
                ["CRYSTA"] = "SUV Premium",
                
                // Sedan Category (typically Dzire, Etios, etc.)
                ["DZIRE"] = "Sedan",
                ["SWIFT DZIRE"] = "Sedan",
                ["ETIOS"] = "Sedan",
                ["AMAZE"] = "Sedan",
                ["XCENT"] = "Sedan",
                ["ASPIRE"] = "Sedan",
                
                // Hatchback Category
                ["SWIFT"] = "Hatchback",
                ["ALTO"] = "Hatchback",
                ["WAGON R"] = "Hatchback",
                ["CELERIO"] = "Hatchback",
                ["GRAND I10"] = "Hatchback",
                
                // SUV Category (7-seater SUVs)
                ["ERTIGA"] = "SUV",
                ["MARAZZO"] = "SUV",
                ["BOLERO"] = "SUV",
                ["SCORPIO"] = "SUV",
                ["XUV500"] = "SUV",
                ["THAR"] = "SUV",
                
                // Luxury/Premium Categories
                ["FORTUNER"] = "SUV Premium",
                ["ENDEAVOUR"] = "SUV Premium",
                ["ALTURAS"] = "SUV Premium",
                
                // Tempo Traveller (if exists)
                ["TEMPO TRAVELLER"] = "Tempo Traveller",
                ["TRAVELLER"] = "Tempo Traveller",
                
                // Bus (if exists)
                ["BUS"] = "Bus",
                ["MINI BUS"] = "Bus",
                
                // Generic mappings for common terms
                ["SEDAN"] = "Sedan",
                ["HATCHBACK"] = "Hatchback",
                ["SUV"] = "SUV",
                ["SUV PREMIUM"] = "SUV Premium",
                
                // Add more mappings as needed based on your CSV data
            };

            _logger.Information("Initialized car model mapping with {Count} entries", mapping.Count);
            return mapping;
        }

        /// <summary>
        /// Maps a car model name to a car category abbreviation
        /// </summary>
        public string MapCarModelToCategory(string carModel)
        {
            if (string.IsNullOrWhiteSpace(carModel))
            {
                _logger.Warning("Empty car model provided for mapping");
                return null;
            }

            var normalizedModel = carModel.Trim().ToUpperInvariant();
            
            // Try exact match first
            if (_carModelToCategoryMapping.ContainsKey(normalizedModel))
            {
                var category = _carModelToCategoryMapping[normalizedModel];
                _logger.Debug("Mapped car model '{CarModel}' to category '{Category}' (exact match)", carModel, category);
                return category;
            }

            // Try partial matches
            foreach (var kvp in _carModelToCategoryMapping)
            {
                var modelKey = kvp.Key.ToUpperInvariant();
                var category = kvp.Value;

                // Check if the car model contains the key or vice versa
                if (normalizedModel.Contains(modelKey) || modelKey.Contains(normalizedModel))
                {
                    _logger.Debug("Mapped car model '{CarModel}' to category '{Category}' (partial match with '{ModelKey}')", 
                        carModel, category, kvp.Key);
                    return category;
                }
            }

            // No mapping found
            _logger.Warning("No mapping found for car model '{CarModel}'. Available mappings: {Mappings}", 
                carModel, string.Join(", ", _carModelToCategoryMapping.Keys));
            return null;
        }

        /// <summary>
        /// Gets all available car model mappings for debugging
        /// </summary>
        public Dictionary<string, string> GetAllMappings()
        {
            return new Dictionary<string, string>(_carModelToCategoryMapping);
        }

        /// <summary>
        /// Adds a new car model mapping at runtime
        /// </summary>
        public void AddMapping(string carModel, string category)
        {
            if (string.IsNullOrWhiteSpace(carModel) || string.IsNullOrWhiteSpace(category))
            {
                _logger.Warning("Invalid car model or category provided for mapping");
                return;
            }

            _carModelToCategoryMapping[carModel.Trim()] = category.Trim();
            _logger.Information("Added new car model mapping: '{CarModel}' -> '{Category}'", carModel, category);
        }
    }

    /// <summary>
    /// Service to map trip type variations to database values
    /// </summary>
    public class TripTypeMappingService
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, string> _tripTypeMapping;

        public TripTypeMappingService(ILogger logger)
        {
            _logger = logger;
            _tripTypeMapping = InitializeTripTypeMapping();
        }

        private Dictionary<string, string> InitializeTripTypeMapping()
        {
            var mapping = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                // Common trip type variations
                ["ONE WAY"] = "One Way",
                ["ONEWAY"] = "One Way",
                ["ONE-WAY"] = "One Way",
                ["SINGLE"] = "One Way",
                
                ["ROUND TRIP"] = "Round Trip",
                ["ROUNDTRIP"] = "Round Trip",
                ["ROUND-TRIP"] = "Round Trip",
                ["RETURN"] = "Round Trip",
                ["TWO WAY"] = "Round Trip",
                
                ["LOCAL"] = "Local",
                ["HOURLY"] = "Local",
                ["CITY TOUR"] = "Local",
                
                // Direct mappings
                ["One Way"] = "One Way",
                ["Round Trip"] = "Round Trip",
                ["Local"] = "Local",
            };

            _logger.Information("Initialized trip type mapping with {Count} entries", mapping.Count);
            return mapping;
        }

        public string MapTripType(string tripType)
        {
            if (string.IsNullOrWhiteSpace(tripType))
            {
                _logger.Warning("Empty trip type provided for mapping");
                return null;
            }

            var normalizedTripType = tripType.Trim();
            
            if (_tripTypeMapping.ContainsKey(normalizedTripType))
            {
                var mappedType = _tripTypeMapping[normalizedTripType];
                _logger.Debug("Mapped trip type '{TripType}' to '{MappedType}'", tripType, mappedType);
                return mappedType;
            }

            _logger.Warning("No mapping found for trip type '{TripType}'. Available mappings: {Mappings}", 
                tripType, string.Join(", ", _tripTypeMapping.Keys));
            return tripType; // Return original if no mapping found
        }

        public Dictionary<string, string> GetAllMappings()
        {
            return new Dictionary<string, string>(_tripTypeMapping);
        }
    }
}
