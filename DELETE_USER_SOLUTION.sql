-- =============================================
-- SIMPLE SOLUTION: Use DELETE instead of TRUNCATE
-- This bypasses foreign key constraint issues with TRUNCATE
-- =============================================

USE [CabYaari]
GO

PRINT '=== USING DELETE INSTEAD OF TRUNCATE ==='
PRINT 'This approach works even with foreign key constraints'
PRINT ''

-- First, clear any remaining referencing data
DELETE FROM [Identity].[RefreshToken];
PRINT 'RefreshToken cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserTokens];
PRINT 'UserTokens cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserRoles];
PRINT 'UserRoles cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserLogins];
PRINT 'UserLogins cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserClaims];
PRINT 'UserClaims cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

PRINT ''
PRINT 'Now deleting from User table...'

-- Use DELETE instead of TRUNCATE
DELETE FROM [Identity].[User];
PRINT 'Users deleted: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

-- Reset identity column if it exists
IF EXISTS (
    SELECT * FROM sys.identity_columns 
    WHERE object_id = OBJECT_ID('[Identity].[User]') 
    AND name = 'Id'
)
BEGIN
    DBCC CHECKIDENT('[Identity].[User]', RESEED, 0)
    PRINT 'Identity column reset to 0'
END

PRINT ''
PRINT '=== VERIFICATION ==='
SELECT COUNT(*) as UserCount FROM [Identity].[User];
SELECT COUNT(*) as RefreshTokenCount FROM [Identity].[RefreshToken];
SELECT COUNT(*) as UserClaimsCount FROM [Identity].[UserClaims];
SELECT COUNT(*) as UserLoginsCount FROM [Identity].[UserLogins];
SELECT COUNT(*) as UserRolesCount FROM [Identity].[UserRoles];
SELECT COUNT(*) as UserTokensCount FROM [Identity].[UserTokens];

PRINT ''
PRINT '=== SUCCESS! USER TABLE CLEARED USING DELETE ==='
PRINT 'Note: DELETE was used instead of TRUNCATE to avoid foreign key issues'
