-- =============================================
-- DEEP ANALYSIS: Find ALL constraints blocking User table truncation
-- This will find constraints we might have missed
-- =============================================

USE [CabYaari]
GO

PRINT '=== COMPREHENSIVE CONSTRAINT ANALYSIS ==='
PRINT ''

-- 1. Find ALL foreign key constraints referencing User table (more detailed)
PRINT '1. ALL FOREIGN KEY CONSTRAINTS REFERENCING USER TABLE:'
SELECT 
    'CONSTRAINT: ' + fk.name AS ConstraintInfo,
    'FROM: [' + SCHEMA_NAME(OBJECTPROPERTY(fk.parent_object_id, 'SchemaId')) + '].[' + OBJECT_NAME(fk.parent_object_id) + '].[' + COL_NAME(fkc.parent_object_id, fkc.parent_column_id) + ']' AS FromTable,
    'TO: [' + SCHEMA_NAME(pk.schema_id) + '].[' + OBJECT_NAME(pk.object_id) + '].[' + COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) + ']' AS ToTable,
    CASE WHEN fk.is_disabled = 1 THEN 'DISABLED' ELSE 'ENABLED' END AS Status,
    'ALTER TABLE [' + SCHEMA_NAME(OBJECTPROPERTY(fk.parent_object_id, 'SchemaId')) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] DROP CONSTRAINT [' + fk.name + '];' AS DropCommand
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.objects pk ON fkc.referenced_object_id = pk.object_id
WHERE fkc.referenced_object_id = OBJECT_ID('[Identity].[User]')
ORDER BY OBJECT_NAME(fk.parent_object_id);

PRINT ''

-- 2. Check for self-referencing constraints in User table
PRINT '2. SELF-REFERENCING CONSTRAINTS IN USER TABLE:'
SELECT 
    'SELF-REF: ' + fk.name AS ConstraintInfo,
    'COLUMN: [' + COL_NAME(fkc.parent_object_id, fkc.parent_column_id) + '] -> [' + COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) + ']' AS SelfReference
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
WHERE fk.parent_object_id = OBJECT_ID('[Identity].[User]')
AND fkc.referenced_object_id = OBJECT_ID('[Identity].[User]');

PRINT ''

-- 3. Find constraints from business tables (outside Identity schema)
PRINT '3. CONSTRAINTS FROM BUSINESS TABLES (NON-IDENTITY SCHEMA):'
SELECT 
    'BUSINESS: ' + fk.name AS ConstraintInfo,
    'FROM: [' + SCHEMA_NAME(OBJECTPROPERTY(fk.parent_object_id, 'SchemaId')) + '].[' + OBJECT_NAME(fk.parent_object_id) + ']' AS FromTable,
    'COLUMN: [' + COL_NAME(fkc.parent_object_id, fkc.parent_column_id) + ']' AS FromColumn
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
WHERE fkc.referenced_object_id = OBJECT_ID('[Identity].[User]')
AND SCHEMA_NAME(OBJECTPROPERTY(fk.parent_object_id, 'SchemaId')) != 'Identity';

PRINT ''

-- 4. Check for any remaining data in tables that reference User
PRINT '4. CHECKING FOR REMAINING DATA IN REFERENCING TABLES:'

-- Check RefreshToken table specifically
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'RefreshToken' AND schema_id = SCHEMA_ID('Identity'))
BEGIN
    DECLARE @refreshCount INT
    SELECT @refreshCount = COUNT(*) FROM [Identity].[RefreshToken]
    PRINT 'RefreshToken table has ' + CAST(@refreshCount AS VARCHAR(10)) + ' rows'
    
    IF @refreshCount > 0
    BEGIN
        PRINT 'Sample RefreshToken data:'
        SELECT TOP 3 * FROM [Identity].[RefreshToken]
    END
END

-- 5. Try alternative approaches
PRINT ''
PRINT '5. ALTERNATIVE SOLUTIONS:'
PRINT ''

-- Solution A: Use DELETE instead of TRUNCATE
PRINT 'SOLUTION A: Use DELETE instead of TRUNCATE'
PRINT 'DELETE FROM [Identity].[User];'
PRINT ''

-- Solution B: Disable specific constraints
PRINT 'SOLUTION B: Disable specific constraints (run the DROP commands above, then:'
PRINT 'TRUNCATE TABLE [Identity].[User];'
PRINT '-- Then recreate constraints if needed'
PRINT ''

-- Solution C: Check if there are triggers
PRINT 'SOLUTION C: Check for triggers on User table:'
SELECT 
    'TRIGGER: ' + tr.name AS TriggerName,
    'TYPE: ' + tr.type_desc AS TriggerType,
    'ENABLED: ' + CASE WHEN tr.is_disabled = 0 THEN 'YES' ELSE 'NO' END AS IsEnabled
FROM sys.triggers tr
WHERE tr.parent_id = OBJECT_ID('[Identity].[User]');

PRINT ''
PRINT '=== IMMEDIATE ACTION PLAN ==='
PRINT 'Try these in order:'
PRINT '1. DELETE FROM [Identity].[User]; (instead of TRUNCATE)'
PRINT '2. If that fails, run the DROP CONSTRAINT commands shown above'
PRINT '3. Then try TRUNCATE again'
