using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using BookingDataImporter.Models;
using Serilog;

namespace BookingDataImporter.Services
{
    public class DatabaseService
    {
        private readonly string _connectionString;
        private readonly ILogger _logger;

        public string ConnectionString => _connectionString;

        public DatabaseService(string connectionString, ILogger logger)
        {
            _connectionString = connectionString;
            _logger = logger;
        }

        public async Task<Dictionary<string, int>> GetCityLookupAsync()
        {
            var cityLookup = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                var query = "SELECT PKID, City_Name FROM RLT_CITY WHERE Is_Active = 1";
                using var command = new SqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    var cityName = reader.GetString("City_Name");
                    var pkid = reader.GetInt32("PKID");
                    cityLookup[cityName] = pkid;
                }
                
                _logger.Information("Loaded {Count} cities from database", cityLookup.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error loading city lookup data");
                throw;
            }
            
            return cityLookup;
        }

        public async Task<Dictionary<string, int>> GetTripTypeLookupAsync()
        {
            var tripTypeLookup = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                var query = "SELECT PKID, Trip_Type FROM RLT_TRIP_TYPES WHERE Is_Active = 1";
                using var command = new SqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    var tripType = reader.GetString("Trip_Type");
                    var pkid = reader.GetInt32("PKID");
                    tripTypeLookup[tripType] = pkid;
                }
                
                _logger.Information("Loaded {Count} trip types from database", tripTypeLookup.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error loading trip type lookup data");
                throw;
            }
            
            return tripTypeLookup;
        }

        public async Task<Dictionary<string, int>> GetCarCategoryLookupAsync()
        {
            var categoryLookup = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                var query = "SELECT PKID, Car_Category_Abbr FROM RLT_CAR_CATEGORY WHERE Is_Active = 1";
                using var command = new SqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    var categoryAbbr = reader.GetString("Car_Category_Abbr");
                    var pkid = reader.GetInt32("PKID");
                    categoryLookup[categoryAbbr] = pkid;
                }
                
                _logger.Information("Loaded {Count} car categories from database", categoryLookup.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error loading car category lookup data");
                throw;
            }
            
            return categoryLookup;
        }

        public async Task<bool> InsertBookingAsync(BookingRecord booking)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                var query = @"
                    INSERT INTO [dbo].[RLT_BOOKING](
                        Booking_Id, City_From_Id, City_To_Id, Trip_Type_Id, Car_Category_Id,
                        Duration, Distance, Basic_Fare, Driver_Charge, GST, Fare, GST_Fare,
                        Coupon_Code, Coupon_Discount, Booking_Date, PickUp_Address, DropOff_Address,
                        PickUp_Date, PickUp_Time, [Name], Mobile_No1, Mobile_No2, Mail_Id, Mode_Of_Payment_Id,
                        Booking_Status_Id, Created_By, Booking_Created_By, razorpay_payment_id, razorpay_order_id,
                        razorpay_signature, razorpay_status, PickUpAddressLatitude, PickUpAddressLongitude,
                        CashAmountToPayDriver, PaymentOption, TollCharge,
                        PaymentType, PartialPaymentAmount, RemainingAmountForDriver, Booking_Remark
                    ) VALUES (
                        @Booking_Id, @City_From_Id, @City_To_Id, @Trip_Type_Id, @Car_Category_Id,
                        @Duration, @Distance, @Basic_Fare, @Driver_Charge, @GST, @Fare, @GST_Fare,
                        @Coupon_Code, @Coupon_Discount, @Booking_Date, @PickUp_Address, @DropOff_Address,
                        @PickUp_Date, @PickUp_Time, @Name, @Mobile_No1, @Mobile_No2, @Mail_Id, @Mode_Of_Payment_Id,
                        @Booking_Status_Id, @Created_By, @Booking_Created_By, @razorpay_payment_id, @razorpay_order_id,
                        @razorpay_signature, @razorpay_status, @PickUpAddressLatitude, @PickUpAddressLongitude,
                        @CashAmountToPayDriver, @PaymentOption, @TollCharge,
                        @PaymentType, @PartialPaymentAmount, @RemainingAmountForDriver, @Booking_Remark
                    )";
                
                using var command = new SqlCommand(query, connection);
                
                // Add parameters
                command.Parameters.AddWithValue("@Booking_Id", booking.Booking_Id ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@City_From_Id", booking.City_From_Id);
                command.Parameters.AddWithValue("@City_To_Id", booking.City_To_Id);
                command.Parameters.AddWithValue("@Trip_Type_Id", booking.Trip_Type_Id);
                command.Parameters.AddWithValue("@Car_Category_Id", booking.Car_Category_Id);
                command.Parameters.AddWithValue("@Duration", booking.Duration ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Distance", booking.Distance ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Basic_Fare", booking.Basic_Fare ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Driver_Charge", booking.Driver_Charge ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@GST", booking.GST ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Fare", booking.Fare ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@GST_Fare", booking.GST_Fare ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Coupon_Code", booking.Coupon_Code ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Coupon_Discount", booking.Coupon_Discount ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Booking_Date", booking.Booking_Date ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PickUp_Address", booking.PickUp_Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@DropOff_Address", booking.DropOff_Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PickUp_Date", booking.PickUp_Date ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PickUp_Time", booking.PickUp_Time ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Name", booking.Name ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Mobile_No1", booking.Mobile_No1 ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Mobile_No2", booking.Mobile_No2 ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Mail_Id", booking.Mail_Id ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Mode_Of_Payment_Id", booking.Mode_Of_Payment_Id ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Booking_Status_Id", booking.Booking_Status_Id ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Created_By", booking.Created_By ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Booking_Created_By", booking.Booking_Created_By ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@razorpay_payment_id", booking.razorpay_payment_id ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@razorpay_order_id", booking.razorpay_order_id ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@razorpay_signature", booking.razorpay_signature ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@razorpay_status", booking.razorpay_status ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PickUpAddressLatitude", booking.PickUpAddressLatitude ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PickUpAddressLongitude", booking.PickUpAddressLongitude ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CashAmountToPayDriver", booking.CashAmountToPayDriver ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PaymentOption", booking.PaymentOption ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TollCharge", booking.TollCharge ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PaymentType", booking.PaymentType ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PartialPaymentAmount", booking.PartialPaymentAmount ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@RemainingAmountForDriver", booking.RemainingAmountForDriver ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Booking_Remark", booking.Booking_Remark ?? (object)DBNull.Value);
                
                await command.ExecuteNonQueryAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error inserting booking {BookingId}", booking.Booking_Id);
                return false;
            }
        }
    }
}
